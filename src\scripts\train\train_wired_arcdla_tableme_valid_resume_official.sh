python main.py ctdet \
  --dataset table_labelmev2 \
  --dataset_name TableLabelMe \
	--exp_id train_tableme_all_wired_valid_resumeofficial \
	--data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_all_wired_valid_configs.py \
  --config_name tableme_full \
	--wiz_4ps \
	--wiz_stacking \
	--wiz_pairloss \
	--tsfm_layers 3 \
	--stacking_layers 3 \
	--batch_size 16 \
	--master_batch_size 6 \
	--arch dla_34 \
	--lr 2e-5 \
	--K 500 \
	--MK 1000 \
	--num_epochs 150 \
	--lr_step '120, 140' \
	--gpus 4,5 \
	--num_workers 8 \
	--val_intervals 5 \
  --load_model /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/ckpt_wired/ckpt_wtw/model_best.pth \
  --load_processor /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/ckpt_wired/ckpt_wtw/processor_best.pth \
  --resume


