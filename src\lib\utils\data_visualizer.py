"""
LORE-TSR 数据可视化工具
用于在训练前验证和保存各种可视化图像，特别是逻辑坐标的可视化
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon
import seaborn as sns
from typing import Dict, Optional, Tuple, List
import json


class LORETSRDataVisualizer:
    """LORE-TSR 数据可视化器"""
    
    def __init__(self, save_dir: str = "debug_visualizations", 
                 mean: List[float] = [0.408, 0.447, 0.470], 
                 std: List[float] = [0.289, 0.274, 0.278]):
        """
        初始化可视化器
        
        Args:
            save_dir: 保存目录
            mean: 图像标准化均值
            std: 图像标准化标准差
        """
        self.save_dir = save_dir
        self.mean = np.array(mean).reshape(3, 1, 1)
        self.std = np.array(std).reshape(3, 1, 1)
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        for subdir in ['input', 'heatmap', 'bbox', 'logic', 'corners', 'combined']:
            os.makedirs(os.path.join(save_dir, subdir), exist_ok=True)
    
    def denormalize_image(self, img_tensor: np.ndarray) -> np.ndarray:
        """
        反标准化图像
        
        Args:
            img_tensor: 标准化后的图像张量 (3, H, W)
            
        Returns:
            反标准化的图像 (H, W, 3)，值范围 [0, 255]
        """
        # 反标准化
        img = img_tensor * self.std + self.mean
        # 转换为 HWC 格式
        img = img.transpose(1, 2, 0)
        # 限制到 [0, 1] 范围并转换为 uint8
        img = np.clip(img * 255, 0, 255).astype(np.uint8)
        return img
    
    def visualize_and_save(self, ret_dict: Dict, sample_id: str, 
                          original_image: Optional[np.ndarray] = None,
                          down_ratio: int = 4) -> None:
        """
        可视化并保存 ret 字典中的所有字段
        
        Args:
            ret_dict: CTDetDataset.__getitem__ 返回的字典
            sample_id: 样本标识符，用于文件命名
            original_image: 原始图像（可选）
            down_ratio: 下采样比例
        """
        print(f"🎨 开始可视化样本: {sample_id}")
        
        # 1. 保存输入图像
        self._save_input_image(ret_dict, sample_id)
        
        # 2. 保存热力图
        self._save_heatmaps(ret_dict, sample_id)
        
        # 3. 保存边界框可视化
        self._save_bbox_visualization(ret_dict, sample_id, down_ratio)
        
        # 4. 保存逻辑坐标可视化（重点功能）
        self._save_logic_visualization(ret_dict, sample_id, down_ratio)
        
        # 5. 保存角点可视化
        self._save_corner_visualization(ret_dict, sample_id, down_ratio)
        
        # 6. 保存综合可视化
        self._save_combined_visualization(ret_dict, sample_id, down_ratio)
        
        # 7. 保存数据统计信息
        self._save_data_statistics(ret_dict, sample_id)
        
        print(f"✅ 样本 {sample_id} 可视化完成，保存至: {self.save_dir}")
    
    def _save_input_image(self, ret_dict: Dict, sample_id: str) -> None:
        """保存输入图像"""
        img = self.denormalize_image(ret_dict['input'])
        save_path = os.path.join(self.save_dir, 'input', f'{sample_id}_input.jpg')
        cv2.imwrite(save_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
    
    def _save_heatmaps(self, ret_dict: Dict, sample_id: str) -> None:
        """保存热力图可视化"""
        hm = ret_dict['hm']  # (num_classes, H, W)
        
        fig, axes = plt.subplots(1, hm.shape[0], figsize=(15, 5))
        if hm.shape[0] == 1:
            axes = [axes]
        
        titles = ['单元格中心热力图', '角点热力图']
        
        for i in range(hm.shape[0]):
            im = axes[i].imshow(hm[i], cmap='hot', interpolation='nearest')
            axes[i].set_title(titles[i] if i < len(titles) else f'热力图 {i}')
            axes[i].axis('off')
            plt.colorbar(im, ax=axes[i])
        
        plt.tight_layout()
        save_path = os.path.join(self.save_dir, 'heatmap', f'{sample_id}_heatmaps.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def _save_bbox_visualization(self, ret_dict: Dict, sample_id: str, down_ratio: int) -> None:
        """保存边界框可视化"""
        img = self.denormalize_image(ret_dict['input'])
        
        # 获取有效对象
        valid_mask = ret_dict['hm_mask'] == 1
        centers = ret_dict['hm_ctxy'][valid_mask] * down_ratio  # 恢复到原图尺寸
        wh_offsets = ret_dict['wh'][valid_mask] * down_ratio
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 12))
        ax.imshow(img)
        
        # 绘制边界框
        for i, (center, wh) in enumerate(zip(centers, wh_offsets)):
            if len(wh) >= 8:  # 确保有足够的坐标
                # 计算四个角点
                corners = []
                for j in range(4):
                    x = center[0] - wh[j*2]
                    y = center[1] - wh[j*2+1]
                    corners.append([x, y])
                
                # 绘制四边形
                polygon = Polygon(corners, fill=False, edgecolor='red', linewidth=2)
                ax.add_patch(polygon)
                
                # 标记中心点
                ax.plot(center[0], center[1], 'ro', markersize=5)
                ax.text(center[0], center[1], str(i), color='white', fontsize=8, 
                       ha='center', va='center', weight='bold')
        
        ax.set_title(f'边界框可视化 - {sample_id}')
        ax.axis('off')
        
        save_path = os.path.join(self.save_dir, 'bbox', f'{sample_id}_bbox.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

    def _save_logic_visualization(self, ret_dict: Dict, sample_id: str, down_ratio: int) -> None:
        """
        保存逻辑坐标可视化（重点功能）
        将逻辑坐标绘制到原图上
        """
        img = self.denormalize_image(ret_dict['input'])

        # 获取有效对象
        valid_mask = ret_dict['hm_mask'] == 1
        centers = ret_dict['hm_ctxy'][valid_mask] * down_ratio
        logic_coords = ret_dict['logic'][valid_mask]  # [start_row, end_row, start_col, end_col]
        wh_offsets = ret_dict['wh'][valid_mask] * down_ratio

        fig, ax = plt.subplots(1, 1, figsize=(15, 15))
        ax.imshow(img)

        # 创建颜色映射
        colors = plt.cm.Set3(np.linspace(0, 1, len(centers)))

        # 绘制每个单元格及其逻辑坐标
        for i, (center, logic, wh, color) in enumerate(zip(centers, logic_coords, wh_offsets, colors)):
            if len(wh) >= 8:
                # 计算四个角点
                corners = []
                for j in range(4):
                    x = center[0] - wh[j*2]
                    y = center[1] - wh[j*2+1]
                    corners.append([x, y])

                # 绘制四边形边界
                polygon = Polygon(corners, fill=True, facecolor=color, alpha=0.3,
                                edgecolor=color, linewidth=2)
                ax.add_patch(polygon)

                # 标记中心点
                ax.plot(center[0], center[1], 'o', color=color, markersize=8)

                # 显示逻辑坐标信息
                start_row, end_row, start_col, end_col = logic.astype(int)

                # 判断是否为合并单元格
                is_merged = (end_row > start_row) or (end_col > start_col)
                merge_info = "合并" if is_merged else "单格"

                # 构建标签文本
                label_text = f"({start_row},{start_col})"
                if is_merged:
                    label_text += f"\n→({end_row},{end_col})"
                    label_text += f"\n{merge_info}"

                # 在单元格中心显示逻辑坐标
                ax.text(center[0], center[1], label_text,
                       color='black', fontsize=10, ha='center', va='center',
                       weight='bold', bbox=dict(boxstyle="round,pad=0.3",
                                              facecolor='white', alpha=0.8))

                # 在角落显示单元格索引
                ax.text(corners[0][0], corners[0][1], str(i),
                       color='red', fontsize=8, weight='bold',
                       bbox=dict(boxstyle="circle,pad=0.1", facecolor='yellow', alpha=0.8))

        ax.set_title(f'逻辑坐标可视化 - {sample_id}\n'
                    f'总计 {len(centers)} 个单元格', fontsize=14)
        ax.axis('off')

        # 添加图例说明
        legend_text = ("图例说明:\n"
                      "• 彩色区域: 单元格边界\n"
                      "• 圆点: 单元格中心\n"
                      "• (r,c): 逻辑坐标(行,列)\n"
                      "• 合并单元格显示起止坐标")
        ax.text(0.02, 0.98, legend_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

        save_path = os.path.join(self.save_dir, 'logic', f'{sample_id}_logic.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

        # 额外保存逻辑坐标的表格形式
        self._save_logic_table(logic_coords[ret_dict['hm_mask'] == 1], sample_id)

    def _save_logic_table(self, logic_coords: np.ndarray, sample_id: str) -> None:
        """保存逻辑坐标的表格形式"""
        logic_data = []
        for i, logic in enumerate(logic_coords):
            start_row, end_row, start_col, end_col = logic.astype(int)
            is_merged = (end_row > start_row) or (end_col > start_col)

            logic_data.append({
                'cell_id': i,
                'start_row': start_row,
                'end_row': end_row,
                'start_col': start_col,
                'end_col': end_col,
                'is_merged': is_merged,
                'row_span': end_row - start_row + 1,
                'col_span': end_col - start_col + 1
            })

        # 保存为JSON文件
        save_path = os.path.join(self.save_dir, 'logic', f'{sample_id}_logic_table.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(logic_data, f, indent=2, ensure_ascii=False)

    def _save_corner_visualization(self, ret_dict: Dict, sample_id: str, down_ratio: int) -> None:
        """保存角点可视化"""
        img = self.denormalize_image(ret_dict['input'])

        # 获取有效角点
        valid_corners = ret_dict['mk_mask'] == 1
        corner_indices = ret_dict['mk_ind'][valid_corners]

        # 获取有效单元格
        valid_cells = ret_dict['hm_mask'] == 1
        cc_match = ret_dict['cc_match'][valid_cells]
        centers = ret_dict['hm_ctxy'][valid_cells] * down_ratio

        fig, ax = plt.subplots(1, 1, figsize=(12, 12))
        ax.imshow(img)

        # 将1D索引转换为2D坐标
        output_h, output_w = ret_dict['hm'].shape[1], ret_dict['hm'].shape[2]

        # 绘制角点
        corner_coords = []
        for idx in corner_indices:
            y = (idx // output_w) * down_ratio
            x = (idx % output_w) * down_ratio
            corner_coords.append([x, y])
            ax.plot(x, y, 'go', markersize=6)

        # 绘制单元格与角点的连接关系
        colors = plt.cm.tab10(np.linspace(0, 1, len(centers)))

        for i, (center, corners_idx, color) in enumerate(zip(centers, cc_match, colors)):
            # 绘制中心点
            ax.plot(center[0], center[1], 'o', color=color, markersize=8)
            ax.text(center[0], center[1], str(i), color='white', fontsize=8,
                   ha='center', va='center', weight='bold')

            # 连接中心点到其四个角点
            for corner_idx in corners_idx:
                if corner_idx < len(corner_coords):
                    corner_x, corner_y = corner_coords[corner_idx]
                    ax.plot([center[0], corner_x], [center[1], corner_y],
                           color=color, alpha=0.6, linewidth=1)

        ax.set_title(f'角点关系可视化 - {sample_id}')
        ax.axis('off')

        save_path = os.path.join(self.save_dir, 'corners', f'{sample_id}_corners.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

    def _save_combined_visualization(self, ret_dict: Dict, sample_id: str, down_ratio: int) -> None:
        """保存综合可视化"""
        img = self.denormalize_image(ret_dict['input'])

        fig, axes = plt.subplots(2, 2, figsize=(20, 20))

        # 1. 原图 + 边界框
        axes[0, 0].imshow(img)
        self._draw_bboxes_on_axis(axes[0, 0], ret_dict, down_ratio)
        axes[0, 0].set_title('边界框检测结果')
        axes[0, 0].axis('off')

        # 2. 热力图叠加
        axes[0, 1].imshow(img)
        hm_overlay = ret_dict['hm'][0] if ret_dict['hm'].shape[0] > 0 else np.zeros_like(img[:,:,0])
        hm_resized = cv2.resize(hm_overlay, (img.shape[1], img.shape[0]))
        axes[0, 1].imshow(hm_resized, alpha=0.5, cmap='hot')
        axes[0, 1].set_title('热力图叠加')
        axes[0, 1].axis('off')

        # 3. 逻辑坐标
        axes[1, 0].imshow(img)
        self._draw_logic_on_axis(axes[1, 0], ret_dict, down_ratio)
        axes[1, 0].set_title('逻辑坐标标注')
        axes[1, 0].axis('off')

        # 4. 数据统计
        self._draw_statistics_on_axis(axes[1, 1], ret_dict)
        axes[1, 1].set_title('数据统计信息')

        plt.tight_layout()
        save_path = os.path.join(self.save_dir, 'combined', f'{sample_id}_combined.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

    def _draw_bboxes_on_axis(self, ax, ret_dict: Dict, down_ratio: int) -> None:
        """在指定轴上绘制边界框"""
        valid_mask = ret_dict['hm_mask'] == 1
        centers = ret_dict['hm_ctxy'][valid_mask] * down_ratio
        wh_offsets = ret_dict['wh'][valid_mask] * down_ratio

        for center, wh in zip(centers, wh_offsets):
            if len(wh) >= 8:
                corners = []
                for j in range(4):
                    x = center[0] - wh[j*2]
                    y = center[1] - wh[j*2+1]
                    corners.append([x, y])

                polygon = Polygon(corners, fill=False, edgecolor='red', linewidth=2)
                ax.add_patch(polygon)

    def _draw_logic_on_axis(self, ax, ret_dict: Dict, down_ratio: int) -> None:
        """在指定轴上绘制逻辑坐标"""
        valid_mask = ret_dict['hm_mask'] == 1
        centers = ret_dict['hm_ctxy'][valid_mask] * down_ratio
        logic_coords = ret_dict['logic'][valid_mask]

        for center, logic in zip(centers, logic_coords):
            start_row, end_row, start_col, end_col = logic.astype(int)
            label = f"({start_row},{start_col})"
            if end_row > start_row or end_col > start_col:
                label += f"\n→({end_row},{end_col})"

            ax.text(center[0], center[1], label, color='red', fontsize=8,
                   ha='center', va='center', weight='bold',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

    def _draw_statistics_on_axis(self, ax, ret_dict: Dict) -> None:
        """在指定轴上绘制数据统计信息"""
        ax.axis('off')

        # 计算统计信息
        valid_cells = (ret_dict['hm_mask'] == 1).sum()
        valid_corners = (ret_dict['mk_mask'] == 1).sum()
        valid_regs = (ret_dict['reg_mask'] == 1).sum()

        logic_coords = ret_dict['logic'][ret_dict['hm_mask'] == 1]
        merged_cells = 0
        max_row, max_col = 0, 0

        for logic in logic_coords:
            start_row, end_row, start_col, end_col = logic.astype(int)
            if end_row > start_row or end_col > start_col:
                merged_cells += 1
            max_row = max(max_row, end_row)
            max_col = max(max_col, end_col)

        # 热力图统计
        hm_max = ret_dict['hm'].max()
        hm_mean = ret_dict['hm'].mean()

        # 构建统计文本
        stats_text = f"""数据统计信息:

📊 基础统计:
• 有效单元格数: {valid_cells}
• 有效角点数: {valid_corners}
• 有效回归点数: {valid_regs}

📋 表格结构:
• 最大行数: {max_row + 1}
• 最大列数: {max_col + 1}
• 合并单元格数: {merged_cells}
• 合并率: {merged_cells/valid_cells*100:.1f}%

🔥 热力图统计:
• 最大值: {hm_max:.3f}
• 平均值: {hm_mean:.3f}

📐 数据维度:
• 输入图像: {ret_dict['input'].shape}
• 热力图: {ret_dict['hm'].shape}
• 边界框: {ret_dict['wh'].shape}
• 逻辑坐标: {ret_dict['logic'].shape}
"""

        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
               fontsize=12, verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))

    def _save_data_statistics(self, ret_dict: Dict, sample_id: str) -> None:
        """保存详细的数据统计信息"""
        stats = self._compute_detailed_statistics(ret_dict)

        save_path = os.path.join(self.save_dir, f'{sample_id}_statistics.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

    def _compute_detailed_statistics(self, ret_dict: Dict) -> Dict:
        """计算详细统计信息"""
        valid_mask = ret_dict['hm_mask'] == 1

        stats = {
            'basic_info': {
                'valid_cells': int(valid_mask.sum()),
                'valid_corners': int((ret_dict['mk_mask'] == 1).sum()),
                'valid_regs': int((ret_dict['reg_mask'] == 1).sum()),
                'input_shape': list(ret_dict['input'].shape),
                'heatmap_shape': list(ret_dict['hm'].shape)
            },
            'heatmap_stats': {
                'max_value': float(ret_dict['hm'].max()),
                'min_value': float(ret_dict['hm'].min()),
                'mean_value': float(ret_dict['hm'].mean()),
                'std_value': float(ret_dict['hm'].std())
            },
            'logic_analysis': {},
            'geometry_analysis': {}
        }

        if valid_mask.sum() > 0:
            logic_coords = ret_dict['logic'][valid_mask]
            centers = ret_dict['hm_ctxy'][valid_mask]

            # 逻辑坐标分析
            merged_count = 0
            row_spans = []
            col_spans = []

            for logic in logic_coords:
                start_row, end_row, start_col, end_col = logic.astype(int)
                row_span = end_row - start_row + 1
                col_span = end_col - start_col + 1

                if row_span > 1 or col_span > 1:
                    merged_count += 1

                row_spans.append(row_span)
                col_spans.append(col_span)

            stats['logic_analysis'] = {
                'merged_cells': merged_count,
                'merge_ratio': merged_count / len(logic_coords),
                'max_row': int(logic_coords[:, 1].max()) + 1,
                'max_col': int(logic_coords[:, 3].max()) + 1,
                'avg_row_span': float(np.mean(row_spans)),
                'avg_col_span': float(np.mean(col_spans)),
                'max_row_span': int(max(row_spans)),
                'max_col_span': int(max(col_spans))
            }

            # 几何分析
            center_x = centers[:, 0]
            center_y = centers[:, 1]

            stats['geometry_analysis'] = {
                'center_x_range': [float(center_x.min()), float(center_x.max())],
                'center_y_range': [float(center_y.min()), float(center_y.max())],
                'center_x_mean': float(center_x.mean()),
                'center_y_mean': float(center_y.mean()),
                'center_distribution_std': [float(center_x.std()), float(center_y.std())]
            }

        return stats


def visualize_training_sample(ret_dict: Dict, sample_id: str, save_dir: str = "debug_visualizations",
                             down_ratio: int = 4, mean: List[float] = None, std: List[float] = None) -> None:
    """
    便捷函数：可视化训练样本

    Args:
        ret_dict: CTDetDataset.__getitem__ 返回的字典
        sample_id: 样本标识符
        save_dir: 保存目录
        down_ratio: 下采样比例
        mean: 图像标准化均值
        std: 图像标准化标准差

    Example:
        # 在 CTDetDataset.__getitem__ 方法中调用
        ret = {'input': inp, 'hm': hm, ...}

        # 添加可视化（仅在调试模式下）
        if self.opt.debug > 0:
            visualize_training_sample(ret, f"sample_{index}",
                                     save_dir="debug_vis",
                                     down_ratio=self.opt.down_ratio)

        return ret
    """
    if mean is None:
        mean = [0.408, 0.447, 0.470]
    if std is None:
        std = [0.289, 0.274, 0.278]

    visualizer = LORETSRDataVisualizer(save_dir=save_dir, mean=mean, std=std)
    visualizer.visualize_and_save(ret_dict, sample_id, down_ratio=down_ratio)
